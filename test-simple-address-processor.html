<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>简化地址处理器测试</title>
    <style>
        body { font-family: monospace; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 6px; }
        .results { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 4px; max-height: 400px; overflow-y: auto; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warn { background: #fff3cd; color: #856404; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        input[type="text"] { width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ddd; border-radius: 4px; }
        .comparison { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 10px 0; }
        .address-box { padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
        .original { background: #fff3cd; }
        .processed { background: #d4edda; }
        pre { white-space: pre-wrap; word-break: break-word; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 简化地址处理器测试</h1>
        <p>测试新的简化地址处理器功能</p>
        
        <div class="test-section">
            <h3>📋 手动测试</h3>
            <input type="text" id="testAddress" placeholder="输入测试地址" 
                   value="ROYCE悦马都高级公寓近双子塔KLCC无边泳池DORMEO DESTINATIONS">
            <button onclick="testSingleAddress()">测试地址处理</button>
            <button onclick="testStaticMapping()">测试静态映射</button>
            <button onclick="testGeminiProcessing()">测试Gemini处理</button>
        </div>

        <div class="test-section">
            <h3>🧪 批量测试</h3>
            <button onclick="testCommonAddresses()">测试常用地址</button>
            <button onclick="testComplexAddresses()">测试复杂地址</button>
            <button onclick="testPerformance()">性能测试</button>
        </div>

        <div class="test-section">
            <h3>📊 系统状态</h3>
            <button onclick="checkSystemStatus()">检查系统状态</button>
            <button onclick="getStats()">获取统计信息</button>
            <button onclick="clearCache()">清空缓存</button>
            <button onclick="clearResults()">清空结果</button>
        </div>

        <div id="addressComparison" style="display: none;">
            <h3>📍 地址对比</h3>
            <div class="comparison">
                <div class="address-box original">
                    <h4>原始地址</h4>
                    <div id="originalAddress"></div>
                </div>
                <div class="address-box processed">
                    <h4>处理后地址</h4>
                    <div id="processedAddress"></div>
                </div>
            </div>
        </div>

        <div class="results" id="testResults"></div>
    </div>

    <!-- 核心系统脚本 -->
    <script src="js/core/logger.js"></script>
    <script src="js/core/dependency-container.js"></script>
    <script src="js/flow/gemini-caller.js"></script>
    <script src="js/flow/simple-address-processor.js"></script>
    <script src="js/data/essential-hotel-data.js"></script>
    <script src="js/flow/knowledge-base.js"></script>

    <script>
        let logs = [];

        // 捕获所有日志
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        function captureLog(level, ...args) {
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : arg
            ).join(' ');
            
            logs.push({
                level,
                message,
                timestamp: new Date().toLocaleTimeString()
            });
            
            updateResults();
            
            // 调用原始方法
            const original = level === 'error' ? originalError : 
                           level === 'warn' ? originalWarn : originalLog;
            original.apply(console, args);
        }

        console.log = (...args) => captureLog('info', ...args);
        console.error = (...args) => captureLog('error', ...args);
        console.warn = (...args) => captureLog('warn', ...args);

        function addResult(message, type = 'info') {
            logs.push({
                level: type,
                message,
                timestamp: new Date().toLocaleTimeString()
            });
            updateResults();
        }

        function updateResults() {
            const container = document.getElementById('testResults');
            container.innerHTML = logs.map(log => {
                const className = log.level === 'error' ? 'error' :
                                log.level === 'warn' ? 'warn' :
                                log.message.includes('✅') ? 'success' : '';
                
                return `<div class="${className}">
                    [${log.timestamp}] <pre>${log.message}</pre>
                </div>`;
            }).join('');
            
            container.scrollTop = container.scrollHeight;
        }

        function clearResults() {
            logs = [];
            updateResults();
            document.getElementById('addressComparison').style.display = 'none';
        }

        function showAddressComparison(original, processed) {
            document.getElementById('originalAddress').textContent = original;
            document.getElementById('processedAddress').textContent = processed;
            document.getElementById('addressComparison').style.display = 'block';
        }

        // 测试单个地址
        async function testSingleAddress() {
            const address = document.getElementById('testAddress').value.trim();
            if (!address) {
                addResult('❌ 请输入测试地址', 'error');
                return;
            }

            try {
                addResult(`🚀 测试地址: ${address}`, 'info');
                
                if (!window.OTA || !window.OTA.simpleAddressProcessor) {
                    throw new Error('简化地址处理器未加载');
                }

                const result = await window.OTA.simpleAddressProcessor.processAddress(address);
                
                addResult(`📊 处理结果: ${JSON.stringify(result, null, 2)}`, 'info');
                
                if (result.success) {
                    showAddressComparison(result.originalAddress, result.processedAddress);
                    addResult(`✅ 地址处理成功: ${result.source} (${result.processingTime}ms)`, 'success');
                } else {
                    addResult(`❌ 地址处理失败: ${result.error}`, 'error');
                }

            } catch (error) {
                addResult(`❌ 测试失败: ${error.message}`, 'error');
            }
        }

        // 测试静态映射
        async function testStaticMapping() {
            addResult('🔍 测试静态映射功能...', 'info');
            
            const staticTestCases = [
                '吉隆坡国际机场',
                'KLCC',
                '双子塔',
                '武吉免登',
                '酒店'
            ];

            try {
                const processor = window.OTA.simpleAddressProcessor;
                
                for (const address of staticTestCases) {
                    const result = await processor.processAddress(address);
                    addResult(`📍 ${address} → ${result.processedAddress} (${result.source})`, 
                             result.source === 'static_mapping' ? 'success' : 'warn');
                }

                addResult('✅ 静态映射测试完成', 'success');

            } catch (error) {
                addResult(`❌ 静态映射测试失败: ${error.message}`, 'error');
            }
        }

        // 测试Gemini处理
        async function testGeminiProcessing() {
            addResult('🤖 测试Gemini处理功能...', 'info');
            
            const complexAddress = 'ROYCE悦马都高级公寓近双子塔KLCC无边泳池DORMEO DESTINATIONS';

            try {
                const processor = window.OTA.simpleAddressProcessor;
                const result = await processor.processAddress(complexAddress);
                
                addResult(`📊 Gemini处理结果: ${JSON.stringify(result, null, 2)}`, 'info');
                
                if (result.success && result.source === 'gemini_ai') {
                    addResult('✅ Gemini处理成功', 'success');
                    showAddressComparison(result.originalAddress, result.processedAddress);
                } else {
                    addResult(`⚠️ Gemini处理结果: ${result.source}`, 'warn');
                }

            } catch (error) {
                addResult(`❌ Gemini测试失败: ${error.message}`, 'error');
            }
        }

        // 测试常用地址
        async function testCommonAddresses() {
            addResult('📋 测试常用地址批量处理...', 'info');
            
            const commonAddresses = [
                '吉隆坡国际机场',
                'KLIA2',
                '双子塔附近的酒店',
                '武吉免登市中心',
                '中央车站'
            ];

            try {
                const processor = window.OTA.simpleAddressProcessor;
                const results = [];

                for (const address of commonAddresses) {
                    const result = await processor.processAddress(address);
                    results.push(result);
                    addResult(`📍 ${address} → ${result.processedAddress} (${result.source}, ${result.processingTime}ms)`, 
                             result.success ? 'success' : 'error');
                }

                const avgTime = results.reduce((sum, r) => sum + r.processingTime, 0) / results.length;
                addResult(`✅ 常用地址测试完成，平均处理时间: ${avgTime.toFixed(2)}ms`, 'success');

            } catch (error) {
                addResult(`❌ 常用地址测试失败: ${error.message}`, 'error');
            }
        }

        // 测试复杂地址
        async function testComplexAddresses() {
            addResult('🧠 测试复杂地址处理...', 'info');
            
            const complexAddresses = [
                'ROYCE悦马都高级公寓近双子塔KLCC无边泳池DORMEO DESTINATIONS',
                '吉隆坡市中心武吉免登星光大道购物中心附近的精品酒店',
                '雪兰莪八打灵再也SS2商业区服务式公寓'
            ];

            try {
                const processor = window.OTA.simpleAddressProcessor;

                for (const address of complexAddresses) {
                    const result = await processor.processAddress(address);
                    addResult(`📍 复杂地址处理:`, 'info');
                    addResult(`   原文: ${result.originalAddress}`, 'info');
                    addResult(`   译文: ${result.processedAddress}`, result.success ? 'success' : 'error');
                    addResult(`   来源: ${result.source} (${result.processingTime}ms)`, 'info');
                }

                addResult('✅ 复杂地址测试完成', 'success');

            } catch (error) {
                addResult(`❌ 复杂地址测试失败: ${error.message}`, 'error');
            }
        }

        // 性能测试
        async function testPerformance() {
            addResult('⚡ 开始性能测试...', 'info');
            
            try {
                const processor = window.OTA.simpleAddressProcessor;
                const testAddress = '双子塔';
                const iterations = 10;
                
                // 第一次调用（无缓存）
                const startTime = Date.now();
                await processor.processAddress(testAddress);
                const firstCallTime = Date.now() - startTime;
                
                // 后续调用（有缓存）
                const cachedTimes = [];
                for (let i = 0; i < iterations; i++) {
                    const start = Date.now();
                    await processor.processAddress(testAddress);
                    cachedTimes.push(Date.now() - start);
                }
                
                const avgCachedTime = cachedTimes.reduce((a, b) => a + b, 0) / cachedTimes.length;
                
                addResult(`📊 性能测试结果:`, 'info');
                addResult(`   首次调用: ${firstCallTime}ms`, 'info');
                addResult(`   缓存调用: ${avgCachedTime.toFixed(2)}ms (平均)`, 'success');
                addResult(`   性能提升: ${((firstCallTime - avgCachedTime) / firstCallTime * 100).toFixed(1)}%`, 'success');

            } catch (error) {
                addResult(`❌ 性能测试失败: ${error.message}`, 'error');
            }
        }

        // 检查系统状态
        function checkSystemStatus() {
            addResult('🔍 检查系统状态...', 'info');
            
            const status = [];
            status.push(`SimpleAddressProcessor: ${!!window.OTA?.simpleAddressProcessor ? '✅' : '❌'}`);
            status.push(`GeminiCaller: ${!!window.OTA?.geminiCaller ? '✅' : '❌'}`);
            status.push(`Logger: ${!!window.logger ? '✅' : '❌'}`);
            
            // 检查向后兼容性
            status.push(`AddressTranslator兼容: ${!!window.OTA?.addressTranslator ? '✅' : '❌'}`);
            status.push(`AddressPipelineCoordinator兼容: ${!!window.OTA?.addressPipelineCoordinator ? '✅' : '❌'}`);
            
            addResult(`系统状态: ${status.join(' | ')}`, 'info');
        }

        // 获取统计信息
        function getStats() {
            try {
                if (window.OTA && window.OTA.simpleAddressProcessor) {
                    const stats = window.OTA.simpleAddressProcessor.getStats();
                    addResult(`📊 统计信息: ${JSON.stringify(stats, null, 2)}`, 'info');
                } else {
                    addResult('❌ 无法获取统计信息', 'error');
                }
            } catch (error) {
                addResult(`❌ 获取统计信息失败: ${error.message}`, 'error');
            }
        }

        // 清空缓存
        function clearCache() {
            try {
                if (window.OTA && window.OTA.simpleAddressProcessor) {
                    window.OTA.simpleAddressProcessor.cleanExpiredCache();
                    addResult('✅ 缓存已清空', 'success');
                } else {
                    addResult('❌ 无法清空缓存', 'error');
                }
            } catch (error) {
                addResult(`❌ 清空缓存失败: ${error.message}`, 'error');
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                addResult('🚀 简化地址处理器测试页面已加载', 'info');
                checkSystemStatus();
            }, 1000);
        });
    </script>
</body>
</html>