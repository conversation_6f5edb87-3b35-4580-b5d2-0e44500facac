<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OTA依赖关系测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        pre {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>🔍 OTA特征识别与配置应用依赖关系测试</h1>
    
    <div class="test-section">
        <h2>📋 测试结果</h2>
        <div id="testResults"></div>
    </div>

    <!-- 按修复后的加载顺序引入文件 -->
    <script src="js/utils.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/ota-channel-config.js"></script>
    <!-- 酒店数据 - OTA系统依赖 -->
    <script src="js/hotel-data-complete.js"></script>
    <!-- 地址翻译服务 - 简化版 -->
    <script src="js/flow/simple-address-processor.js"></script>
    <!-- OTA策略系统 -->
    <script src="js/ota-strategies.js"></script>

    <script>
        // 测试脚本
        function runDependencyTests() {
            const results = [];
            const resultDiv = document.getElementById('testResults');

            // 测试1: 基础依赖检查
            results.push('=== 1. 基础依赖检查 ===');
            
            if (window.OTA && window.OTA.hotelData) {
                results.push('✅ hotel-data-complete已加载');
                results.push(`   - 数据库条目数: ${Object.keys(window.OTA.hotelData).length}`);
            } else {
                results.push('❌ hotel-data-complete未加载');
            }

            if (window.OTA && window.OTA.addressTranslator) {
                results.push('✅ address-translator已注册');
                const stats = window.OTA.addressTranslator.getTranslationStats();
                results.push(`   - 缓存大小: ${stats.cacheSize}`);
                results.push(`   - 支持语言: ${stats.supportedLanguages.join(', ')}`);
            } else {
                results.push('❌ address-translator未注册');
            }

            if (window.OTA && window.OTA.customizationEngine) {
                results.push('✅ OTA定制化引擎已加载');
            } else {
                results.push('❌ OTA定制化引擎未加载');
            }

            // 测试2: Fliggy配置测试
            results.push('\n=== 2. Fliggy配置测试 ===');
            
            try {
                if (window.OTA && window.OTA.customizationEngine) {
                    const fliggyConfig = window.OTA.customizationEngine.getChannelConfig('Fliggy');
                    results.push('✅ Fliggy配置获取成功');
                    results.push(`   - 价格计算器: ${typeof fliggyConfig.priceCalculator}`);
                    results.push(`   - 字段处理: ${Object.keys(fliggyConfig.fieldProcessing).length}个字段`);
                    
                    // 测试客户名字处理
                    const testOrder = {
                        contact_name: '张先生',
                        buyer_name: '李女士',
                        customer_name: '王先生'
                    };
                    const customerNameProcessor = fliggyConfig.fieldProcessing.customer_name;
                    if (typeof customerNameProcessor === 'function') {
                        const result = customerNameProcessor(testOrder.customer_name, testOrder);
                        results.push(`   - 客户名字处理测试: ${result} (应为: 张先生)`);
                        if (result === '张先生') {
                            results.push('   ✅ 客户名字优先级逻辑正确');
                        } else {
                            results.push('   ❌ 客户名字优先级逻辑错误');
                        }
                    }
                } else {
                    results.push('❌ 无法获取Fliggy配置');
                }
            } catch (error) {
                results.push(`❌ Fliggy配置测试失败: ${error.message}`);
            }

            // 测试3: 地址翻译测试
            results.push('\n=== 3. 地址翻译测试 ===');
            
            try {
                if (window.OTA && window.OTA.addressTranslator) {
                    // 测试中文地址翻译
                    const testAddresses = ['新山万丽酒店', '吉隆坡希尔顿酒店', '不存在的酒店'];

                    for (const address of testAddresses) {
                        const result = await window.OTA.addressTranslator.translateAddress(address);
                        results.push(`   - "${address}": ${result.translations.en || '未找到'}`);
                    }

                    // 测试中文检测
                    const hasChinese = /[\u4e00-\u9fa5]/.test('新山万丽酒店');
                    results.push(`   - 中文检测: ${hasChinese ? '✅' : '❌'}`);

                } else {
                    results.push('❌ 地址翻译服务不可用');
                }
            } catch (error) {
                results.push(`❌ 地址翻译测试失败: ${error.message}`);
            }

            // 测试4: OTA检测测试
            results.push('\n=== 4. OTA检测测试 ===');
            
            try {
                if (window.OTA && window.OTA.channelDetector) {
                    const testText = '订单编号1234567890123456789 客户：张先生';
                    const detectionResult = window.OTA.channelDetector.detectChannel(testText);
                    
                    results.push(`   - 测试文本: ${testText}`);
                    results.push(`   - 检测结果: ${detectionResult.detectedChannel || '未检测到'}`);
                    results.push(`   - 置信度: ${detectionResult.confidence}`);
                    results.push(`   - 方法: ${detectionResult.method || '无'}`);

                    if (detectionResult.detectedChannel === 'Fliggy') {
                        results.push('   ✅ Fliggy检测正确');
                    } else {
                        results.push('   ❌ Fliggy检测失败');
                    }
                } else {
                    results.push('❌ OTA渠道检测器不可用');
                }
            } catch (error) {
                results.push(`❌ OTA检测测试失败: ${error.message}`);
            }

            // 测试5: 异步处理测试
            results.push('\n=== 5. 异步处理测试 ===');
            
            (async () => {
                try {
                    if (window.OTA && window.OTA.customizationEngine) {
                        const testOrderData = {
                            customer_name: '测试客户',
                            contact_name: '联系人张先生',
                            buyer_name: '买家李女士',
                            pickup_location: '新山万丽酒店',
                            dropoff_location: 'KLIA机场',
                            passenger_count: 3,
                            orderContent: 'malaysia kuala lumpur'
                        };

                        const result = await window.OTA.customizationEngine.processChannelFields('Fliggy', testOrderData);
                        results.push('   ✅ 异步字段处理成功');
                        results.push(`   - 处理后客户名: ${result.customer_name}`);
                        results.push(`   - 上车地点英文: ${result.pickup_location_en || '未翻译'}`);
                        
                        // 测试价格计算
                        const priceResult = window.OTA.customizationEngine.calculateChannelPrice('Fliggy', 100, result);
                        if (priceResult.success) {
                            results.push(`   - 价格计算: ${priceResult.originalPrice} → ${priceResult.finalPrice}`);
                            results.push(`   - 调整项: ${priceResult.adjustments.length}个`);
                        }

                    } else {
                        results.push('❌ 定制化引擎不可用');
                    }
                } catch (error) {
                    results.push(`❌ 异步处理测试失败: ${error.message}`);
                }

                // 显示结果
                resultDiv.innerHTML = '<pre>' + results.join('\n') + '</pre>';
            })();
        }

        // 等待所有脚本加载完成后运行测试
        window.addEventListener('load', () => {
            setTimeout(runDependencyTests, 1000); // 延迟1秒确保所有模块初始化完成
        });
    </script>
</body>
</html>
