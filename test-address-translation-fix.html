<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地址翻译修复测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f7fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e1e8ed;
            border-radius: 8px;
            background-color: #fafbfc;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: background 0.3s;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .results {
            margin-top: 20px;
            padding: 15px;
            background: white;
            border-radius: 6px;
            border-left: 4px solid #28a745;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        .error {
            border-left-color: #dc3545;
            color: #721c24;
            background-color: #f8d7da;
        }
        .info {
            border-left-color: #17a2b8;
            color: #0c5460;
            background-color: #d1ecf1;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .system-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
        }
        .log-success { background-color: #d4edda; color: #155724; }
        .log-error { background-color: #f8d7da; color: #721c24; }
        .log-warn { background-color: #fff3cd; color: #856404; }
        .log-info { background-color: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 地址翻译修复测试</h1>
            <p>测试修复后的中文地址翻译功能</p>
        </div>

        <div class="system-info" id="systemInfo">
            <h3>系统状态检查</h3>
            <div id="systemStatus">正在检查...</div>
        </div>

        <div class="test-section">
            <h3>📋 测试用例</h3>
            <button class="test-button" onclick="testAddressTranslation()">测试中文地址翻译</button>
            <button class="test-button" onclick="testGeminiConnection()">测试Gemini连接</button>
            <button class="test-button" onclick="testPipelineExecution()">测试地址处理流水线</button>
            <button class="test-button" onclick="clearResults()">清空结果</button>
            <div class="results" id="testResults"></div>
        </div>

        <div class="test-section">
            <h3>📊 实时日志</h3>
            <div class="results" id="realTimeLog"></div>
        </div>
    </div>

    <!-- 核心系统脚本 -->
    <script src="js/core/logger.js"></script>
    <script src="js/core/dependency-container.js"></script>
    <script src="js/flow/gemini-caller.js"></script>
    <script src="js/flow/simple-address-processor.js"></script>
    <script src="js/data/essential-hotel-data.js"></script>
    <script src="js/flow/knowledge-base.js"></script>

    <script>
        let testResults = [];
        let logEntries = [];

        // 自定义日志处理器，捕获所有日志
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;

        function captureLog(level, ...args) {
            const entry = {
                level,
                message: args.map(arg => typeof arg === 'object' ? JSON.stringify(arg, null, 2) : arg).join(' '),
                timestamp: new Date().toLocaleTimeString()
            };
            
            logEntries.push(entry);
            updateRealTimeLog();
            
            // 调用原始console方法
            const originalMethod = level === 'error' ? originalConsoleError : 
                                 level === 'warn' ? originalConsoleWarn : originalConsoleLog;
            originalMethod.apply(console, args);
        }

        console.log = (...args) => captureLog('info', ...args);
        console.error = (...args) => captureLog('error', ...args);
        console.warn = (...args) => captureLog('warn', ...args);

        function updateRealTimeLog() {
            const logContainer = document.getElementById('realTimeLog');
            const recentLogs = logEntries.slice(-50); // 显示最近50条日志
            
            logContainer.innerHTML = recentLogs.map(entry => 
                `<div class="log-entry log-${entry.level}">[${entry.timestamp}] ${entry.message}</div>`
            ).join('');
            
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function addResult(message, type = 'info') {
            testResults.push({
                message,
                type,
                timestamp: new Date().toLocaleTimeString()
            });
            updateResults();
        }

        function updateResults() {
            const resultsContainer = document.getElementById('testResults');
            resultsContainer.innerHTML = testResults.map(result => {
                const className = result.type === 'error' ? 'error' : 
                                result.type === 'success' ? '' : 'info';
                return `<div class="${className}">[${result.timestamp}] ${result.message}</div>`;
            }).join('\n');
        }

        function clearResults() {
            testResults = [];
            logEntries = [];
            updateResults();
            updateRealTimeLog();
        }

        // 系统状态检查
        function checkSystemStatus() {
            const status = [];
            
            // 检查核心服务
            status.push(`Logger: ${!!window.logger ? '✅' : '❌'}`);
            status.push(`DependencyContainer: ${!!window.DependencyContainer ? '✅' : '❌'}`);
            status.push(`OTA命名空间: ${!!window.OTA ? '✅' : '❌'}`);
            
            // 检查Gemini服务
            status.push(`GeminiCaller: ${!!(window.OTA && window.OTA.geminiCaller) ? '✅' : '❌'}`);
            status.push(`AddressTranslator: ${!!(window.OTA && window.OTA.addressTranslator) ? '✅' : '❌'}`);
            status.push(`AddressPipelineCoordinator: ${!!(window.OTA && window.OTA.addressPipelineCoordinator) ? '✅' : '❌'}`);
            
            // 检查知识库
            status.push(`KnowledgeBase: ${!!(window.OTA && window.OTA.knowledgeBase) ? '✅' : '❌'}`);
            status.push(`EssentialHotelData: ${!!(window.essentialHotelData && window.essentialHotelData.loaded) ? '✅' : '❌'}`);

            document.getElementById('systemStatus').innerHTML = status.join('<br>');
        }

        // 测试Gemini连接
        async function testGeminiConnection() {
            addResult('🔍 开始测试Gemini连接...', 'info');
            
            try {
                if (!window.OTA || !window.OTA.geminiCaller) {
                    throw new Error('GeminiCaller未找到');
                }

                const testPrompt = '请回复"连接测试成功"这几个字。';
                const result = await window.OTA.geminiCaller.callAPI(testPrompt, 'text', { temperature: 0.1 });
                
                addResult(`✅ Gemini连接测试成功`, 'success');
                addResult(`响应数据: ${JSON.stringify(result, null, 2)}`, 'info');
                
            } catch (error) {
                addResult(`❌ Gemini连接测试失败: ${error.message}`, 'error');
                console.error('Gemini连接测试错误:', error);
            }
        }

        // 测试中文地址翻译
        async function testAddressTranslation() {
            addResult('🚀 开始测试中文地址翻译...', 'info');
            
            const testAddresses = [
                'ROYCE悦马都高级公寓近双子塔KLCC无边泳池DORMEO DESTINATIONS',
                '吉隆坡国际机场T2'
            ];

            try {
                if (!window.OTA || !window.OTA.addressTranslator) {
                    throw new Error('AddressTranslator未找到');
                }

                const translator = window.OTA.addressTranslator;

                for (const address of testAddresses) {
                    addResult(`📍 测试地址: ${address}`, 'info');
                    
                    // 测试直接翻译
                    const translationResult = await translator.translateAddress(address, ['en', 'ms']);
                    addResult(`翻译结果: ${JSON.stringify(translationResult, null, 2)}`, 'info');
                    
                    // 测试Gemini处理
                    if (translator.processWithGemini) {
                        const geminiResult = await translator.processWithGemini(address);
                        addResult(`Gemini处理结果: ${JSON.stringify(geminiResult, null, 2)}`, geminiResult.success ? 'success' : 'error');
                    } else {
                        addResult('⚠️ processWithGemini方法不可用', 'warn');
                    }
                }

                addResult('✅ 地址翻译测试完成', 'success');
                
            } catch (error) {
                addResult(`❌ 地址翻译测试失败: ${error.message}`, 'error');
                console.error('地址翻译测试错误:', error);
            }
        }

        // 测试地址处理流水线
        async function testPipelineExecution() {
            addResult('🔄 开始测试地址处理流水线...', 'info');
            
            try {
                if (!window.OTA || !window.OTA.addressPipelineCoordinator) {
                    // 尝试创建流水线协调器实例
                    if (window.AddressPipelineCoordinator) {
                        window.OTA = window.OTA || {};
                        window.OTA.addressPipelineCoordinator = new window.AddressPipelineCoordinator();
                        addResult('✅ 已创建AddressPipelineCoordinator实例', 'success');
                    } else {
                        throw new Error('AddressPipelineCoordinator类未找到');
                    }
                }

                const coordinator = window.OTA.addressPipelineCoordinator;
                const testAddress = 'ROYCE悦马都高级公寓近双子塔KLCC无边泳池DORMEO DESTINATIONS';
                
                addResult(`📍 测试地址: ${testAddress}`, 'info');
                
                // 处理地址
                const result = await coordinator.processAddress(testAddress);
                
                addResult(`流水线处理结果: ${JSON.stringify(result, null, 2)}`, result.success ? 'success' : 'error');
                addResult('✅ 地址处理流水线测试完成', 'success');
                
            } catch (error) {
                addResult(`❌ 地址处理流水线测试失败: ${error.message}`, 'error');
                console.error('地址处理流水线测试错误:', error);
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 等待系统加载
            setTimeout(() => {
                checkSystemStatus();
                addResult('🚀 测试页面已加载，可以开始测试', 'info');
            }, 1000);
        });

        // 定期更新系统状态
        setInterval(checkSystemStatus, 5000);
    </script>
</body>
</html>