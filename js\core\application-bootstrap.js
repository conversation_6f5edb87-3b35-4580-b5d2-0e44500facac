/**
 * 依赖标签（Dependency Tags）
 * 文件: js/core/application-bootstrap.js
 * 角色: 启动引导（依赖→服务→管理器→UI→收尾），暴露 window.OTA.debug 工具
 * 上游依赖(直接使用): DependencyContainer, ServiceLocator, I18n
 * 下游被依赖(常见调用方): index.html / main.js 启动流程
 * 事件: 分阶段初始化日志与报表（getStartupReport/restart）；严格的脚本顺序
 * 更新时间: 2025-08-09
 */
/**
 * 应用启动协调器
 * 统一管理应用的启动流程，解决初始化时序混乱问题
 * 
 * 启动阶段:
 * 1. dependencies - 注册所有依赖
 * 2. services - 初始化核心服务
 * 3. managers - 初始化管理器
 * 4. ui - 初始化用户界面
 * 5. finalization - 完成启动
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * 应用启动协调器类
     */
    class ApplicationBootstrap {
        constructor() {
            this.phases = [
                'dependencies',
                'services',
                'managers', 
                'ui',
                'finalization'
            ];
            this.currentPhase = 0;
            this.startTime = null;
            this.phaseResults = [];
            this.container = null;
            this.serviceLocator = null;
        }

        /**
         * 启动应用
         */
        async start() {
            this.startTime = performance.now();
            console.log('🚀 开始启动OTA订单处理系统...');

            try {
                // 获取依赖容器和服务定位器
                this.container = window.OTA.container;
                this.serviceLocator = window.OTA.serviceLocator;

                if (!this.container) {
                    throw new Error('依赖容器未初始化');
                }

                // 逐阶段执行启动流程
                for (let i = 0; i < this.phases.length; i++) {
                    this.currentPhase = i;
                    const phase = this.phases[i];
                    
                    console.log(`📋 执行启动阶段: ${phase} (${i + 1}/${this.phases.length})`);
                    
                    const phaseStart = performance.now();
                    const result = await this.executePhase(phase);
                    const phaseEnd = performance.now();
                    
                    this.phaseResults.push({
                        phase,
                        success: result.success,
                        duration: phaseEnd - phaseStart,
                        details: result.details,
                        errors: result.errors || []
                    });

                    if (!result.success) {
                        throw new Error(`启动阶段 ${phase} 失败: ${result.errors.join(', ')}`);
                    }
                }

                const totalTime = performance.now() - this.startTime;
                console.log(`✅ OTA系统启动完成，总耗时: ${totalTime.toFixed(2)}ms`);
                
                this.printStartupReport();
                return { success: true, duration: totalTime };

            } catch (error) {
                console.error('❌ 系统启动失败:', error);
                this.printFailureReport(error);
                return { success: false, error: error.message };
            }
        }

        /**
         * 执行启动阶段
         * @param {string} phase - 阶段名称
         */
        async executePhase(phase) {
            const result = { success: true, details: [], errors: [] };

            try {
                switch (phase) {
                    case 'dependencies':
                        await this.registerDependencies(result);
                        break;
                    case 'services':
                        await this.initializeServices(result);
                        break;
                    case 'managers':
                        await this.initializeManagers(result);
                        break;
                    case 'ui':
                        await this.initializeUI(result);
                        break;
                    case 'finalization':
                        await this.finalize(result);
                        break;
                    default:
                        throw new Error(`未知的启动阶段: ${phase}`);
                }
            } catch (error) {
                result.success = false;
                result.errors.push(error.message);
            }

            return result;
        }

        /**
         * 注册所有依赖
         */
        async registerDependencies(result) {
            const dependencies = [
                // 核心服务
                { name: 'appState', factory: () => window.OTA.appState || new window.AppState() },
                { name: 'logger', factory: () => window.OTA.logger || window.logger },
                { name: 'utils', factory: () => window.OTA.utils || window.utils },
                
                // 🔧 事件协调器 - 关键修复：确保事件协调器被正确初始化
                { name: 'eventCoordinator', factory: () => {
                    const coordinator = window.OTA.globalEventCoordinator || window.globalEventCoordinator;
                    if (coordinator && !coordinator.initialized) {
                        coordinator.init();
                    }
                    return coordinator;
                }},


                // 业务服务
                { name: 'apiService', factory: () => window.OTA.apiService || window.apiService },
                { name: 'geminiService', factory: () => {
                    // 优先使用适配器（保持向后兼容）
                    if (window.OTA.geminiService) return window.OTA.geminiService;
                    // 回退到全局geminiService
                    if (window.geminiService) return window.geminiService;
                    // 回退到获取函数
                    if (window.getGeminiService) return window.getGeminiService();
                    return null;
                }},
                { name: 'i18nManager', factory: () => window.OTA.i18nManager || window.i18nManager },

                // 功能管理器（带错误处理）
                // 🚀 关键路径优化：FormManager实例注册
                { name: 'formManager', factory: () => {
                    try {
                        // 优先从UIManager获取已初始化的实例
                        if (window.OTA.uiManager?.managers?.form) {
                            return window.OTA.uiManager.managers.form;
                        }
                        // 降级到全局获取
                        if (window.formManager) {
                            return window.formManager;
                        }
                        // 最后尝试从服务定位器获取
                        if (window.getFormManager) {
                            return window.getFormManager();
                        }
                        return null;
                    } catch (e) {
                        console.warn('[ApplicationBootstrap] FormManager获取失败:', e.message);
                        return null;
                    }
                }},
                { name: 'imageUploadManager', factory: () => {
                    try {
                        return window.OTA.imageUploadManager ||
                               (window.getImageUploadManager && window.getImageUploadManager());
                    } catch (e) { return null; }
                }},
                { name: 'currencyConverter', factory: () => {
                    try {
                        return window.OTA.currencyConverter || 
                               (window.getCurrencyConverter && window.getCurrencyConverter());
                    } catch (e) { return null; }
                }},
                { name: 'multiOrderManager', factory: () => {
                    try {
                        return window.OTA.multiOrderManager || 
                               (window.getMultiOrderManager && window.getMultiOrderManager());
                    } catch (e) { return null; }
                }},
                { name: 'orderHistoryManager', factory: () => {
                    try {
                        return window.OTA.orderHistoryManager || 
                               (window.getOrderHistoryManager && window.getOrderHistoryManager());
                    } catch (e) { return null; }
                }},
                { name: 'pagingServiceManager', factory: () => {
                    try {
                        return window.OTA.pagingServiceManager || 
                               (window.getPagingServiceManager && window.getPagingServiceManager());
                    } catch (e) { return null; }
                }},

                // UI管理器
                { name: 'uiManager', factory: () => {
                    try {
                        return window.OTA.uiManager || 
                               (window.getUIManager && window.getUIManager());
                    } catch (e) { return null; }
                }},

                // 渠道检测器（使用新架构实现）
                { name: 'channelDetector', factory: () => {
                    try {
                        // 优先使用已存在的全局实例
                        if (window.OTA && window.OTA.channelDetector && typeof window.OTA.channelDetector.detectChannel === 'function') {
                            return window.OTA.channelDetector;
                        }

                        // 使用新架构的子层实现
                        const FlowCtor = (window.OTA && window.OTA.ChannelDetector) || window.ChannelDetector;
                        if (typeof FlowCtor === 'function') {
                            const instance = new FlowCtor();
                            window.OTA = window.OTA || {};
                            window.OTA.channelDetector = instance;
                            return instance;
                        }

                        // 安全回退
                        return null;
                    } catch (e) {
                        console.warn('渠道检测器初始化失败:', e);
                        return null;
                    }
                }}
            ];

            for (const dep of dependencies) {
                try {
                    this.container.register(dep.name, dep.factory);
                    result.details.push(`已注册: ${dep.name}`);
                } catch (error) {
                    result.errors.push(`注册 ${dep.name} 失败: ${error.message}`);
                }
            }

            console.log(`📦 已注册 ${result.details.length} 个依赖`);
        }

        /**
         * 初始化核心服务
         */
        async initializeServices(result) {
            const coreServices = ['appState', 'logger', 'utils', 'eventCoordinator', 'apiService', 'geminiService'];
            
            for (const serviceName of coreServices) {
                try {
                    const service = this.container.get(serviceName);
                    if (service && typeof service.init === 'function') {
                        await service.init();
                    }
                    result.details.push(`已初始化: ${serviceName}`);
                } catch (error) {
                    result.errors.push(`初始化 ${serviceName} 失败: ${error.message}`);
                }
            }

            // 确保渠道检测器实例与全局对齐（避免被后续脚本覆盖）
            try {
                if (this.container.has('channelDetector')) {
                    const detector = this.container.get('channelDetector');
                    if (detector && (!window.OTA.channelDetector || window.OTA.channelDetector !== detector)) {
                        window.OTA.channelDetector = detector;
                    }
                }
            } catch (_) { /* 忽略对齐失败，不阻断启动 */ }

            console.log(`⚙️ 已初始化 ${result.details.length} 个核心服务`);
        }

        /**
         * 初始化管理器
         */
        async initializeManagers(result) {
            const managers = [
                'imageUploadManager',
                'currencyConverter', 
                'multiOrderManager',
                'orderHistoryManager',
                'pagingServiceManager'
            ];

            for (const managerName of managers) {
                try {
                    const manager = this.container.get(managerName);
                    if (manager && typeof manager.init === 'function') {
                        await manager.init();
                    }
                    result.details.push(`已初始化: ${managerName}`);
                } catch (error) {
                    // 管理器初始化失败不阻断启动流程
                    result.details.push(`跳过: ${managerName} (${error.message})`);
                }
            }

            console.log(`🎛️ 已处理 ${result.details.length} 个管理器`);
        }

        /**
         * 初始化用户界面
         */
        async initializeUI(result) {
            try {
                // **修复时序问题**: 先初始化国际化管理器，再初始化UI管理器
                const i18nManager = this.container.get('i18nManager');
                if (i18nManager && typeof i18nManager.init === 'function') {
                    await i18nManager.init();
                    result.details.push('国际化管理器已初始化');
                    // 等待一小段时间确保i18n完全初始化
                    await new Promise(resolve => setTimeout(resolve, 10));
                }

                const uiManager = this.container.get('uiManager');
                if (uiManager && typeof uiManager.init === 'function') {
                    await uiManager.init();
                    result.details.push('UI管理器已初始化');
                }

                console.log('🎨 用户界面初始化完成');
            } catch (error) {
                result.errors.push(`UI初始化失败: ${error.message}`);
            }
        }

        /**
         * 完成启动
         */
        async finalize(result) {
            try {
                // 执行系统健康检查
                const healthCheck = this.performHealthCheck();
                result.details.push(`健康检查: ${healthCheck.score}/100`);

                // 设置全局错误处理
                this.setupGlobalErrorHandling();
                result.details.push('全局错误处理已设置');

                // 暴露调试接口
                this.exposeDebugInterface();
                result.details.push('调试接口已暴露');

                // 强制初始化字段标准化层（确保拦截器安装）
                this.initializeFieldStandardization();
                result.details.push('字段标准化层初始化完成');

                console.log('🏁 系统启动完成');
            } catch (error) {
                result.errors.push(`完成阶段失败: ${error.message}`);
            }
        }

        /**
         * 初始化字段标准化层
         */
        initializeFieldStandardization() {
            try {
                // 检查字段标准化层是否存在
                const fieldStandardizationLayer = window.OTA?.globalFieldStandardizationLayer || 
                                                 window.getGlobalFieldStandardizationLayer?.();
                
                if (fieldStandardizationLayer) {
                    // 检查特性开关
                    const enabled = window.OTA?.featureToggle?.isEnabled('enableGlobalFieldStandardization');
                    console.log('[ApplicationBootstrap] 字段标准化层状态检查', {
                        layerExists: !!fieldStandardizationLayer,
                        featureEnabled: enabled,
                        initialized: fieldStandardizationLayer.initialized
                    });
                    
                    if (enabled && !fieldStandardizationLayer.initialized) {
                        console.log('[ApplicationBootstrap] 🔧 强制启用字段标准化层...');
                        fieldStandardizationLayer.initialize();
                        console.log('[ApplicationBootstrap] ✅ 字段标准化层已强制启用');
                    } else if (fieldStandardizationLayer.initialized) {
                        console.log('[ApplicationBootstrap] ✅ 字段标准化层已初始化');
                    } else {
                        console.log('[ApplicationBootstrap] ⚠️ 字段标准化层未启用（特性开关关闭）');
                    }
                } else {
                    console.warn('[ApplicationBootstrap] ⚠️ 未找到字段标准化层');
                }
            } catch (error) {
                console.error('[ApplicationBootstrap] 字段标准化层初始化失败:', error);
            }
        }

        /**
         * 执行系统健康检查
         */
        performHealthCheck() {
            let score = 100;
            const issues = [];

            // 检查核心服务
            const coreServices = ['appState', 'logger', 'apiService', 'geminiService', 'uiManager'];
            for (const service of coreServices) {
                if (!this.container.has(service)) {
                    score -= 15;
                    issues.push(`缺少核心服务: ${service}`);
                }
            }

            // 检查DOM元素
            const criticalElements = ['loginPanel', 'workspace', 'orderTextInput'];
            for (const elementId of criticalElements) {
                if (!document.getElementById(elementId)) {
                    score -= 10;
                    issues.push(`缺少关键DOM元素: ${elementId}`);
                }
            }

            return { score: Math.max(0, score), issues };
        }

        /**
         * 设置全局错误处理
         * 浏览器兼容性修复：增强 Promise 错误处理，特别针对 Safari 和 Edge
         */
        setupGlobalErrorHandling() {
            window.addEventListener('error', (event) => {
                const logger = this.serviceLocator?.getService('logger');
                if (logger) {
                    logger.logError('全局错误', event.error);
                } else {
                    console.error('全局错误:', event.error);
                }
            });

            window.addEventListener('unhandledrejection', (event) => {
                const logger = this.serviceLocator?.getService('logger');

                // 浏览器兼容性处理：检查是否是系统完整性检查的错误
                const isIntegrityCheckError = event.reason?.message?.includes('multiOrderManagerV2 未找到') ||
                                            event.reason?.message?.includes('服务') && event.reason?.message?.includes('未找到');

                if (isIntegrityCheckError) {
                    // 对于完整性检查错误，降级为警告处理，不阻止应用运行
                    console.warn('🔍 系统完整性检查警告（不影响应用运行）:', event.reason?.message);
                    event.preventDefault(); // 阻止浏览器的默认错误处理

                    if (logger) {
                        logger.log('系统完整性检查警告', 'warning', { error: event.reason?.message });
                    }
                } else {
                    // 其他 Promise 错误正常处理
                    if (logger) {
                        logger.logError('未处理的Promise拒绝', event.reason);
                    } else {
                        console.error('未处理的Promise拒绝:', event.reason);
                    }
                }
            });
        }

        /**
         * 暴露调试接口
         */
        exposeDebugInterface() {
            window.OTA.debug = {
                bootstrap: this,
                container: this.container,
                serviceLocator: this.serviceLocator,
                getService: (name) => this.serviceLocator?.getService(name),
                getStartupReport: () => this.getStartupReport(),
                restart: () => this.restart()
            };
        }

        /**
         * 获取启动报告
         */
        getStartupReport() {
            return {
                totalDuration: this.phaseResults.reduce((sum, phase) => sum + phase.duration, 0),
                phases: this.phaseResults,
                success: this.phaseResults.every(phase => phase.success)
            };
        }

        /**
         * 打印启动报告
         */
        printStartupReport() {
            console.group('📊 启动报告');
            this.phaseResults.forEach(phase => {
                const status = phase.success ? '✅' : '❌';
                console.log(`${status} ${phase.phase}: ${phase.duration.toFixed(2)}ms`);
                if (phase.details.length > 0) {
                    console.log(`   详情: ${phase.details.join(', ')}`);
                }
            });
            console.groupEnd();
        }

        /**
         * 打印失败报告
         */
        printFailureReport(error) {
            console.group('❌ 启动失败报告');
            console.error('错误:', error.message);
            console.log('已完成阶段:', this.phaseResults.filter(p => p.success).map(p => p.phase));
            console.log('失败阶段:', this.phaseResults.filter(p => !p.success).map(p => p.phase));
            console.groupEnd();
        }

        /**
         * 重启应用
         */
        async restart() {
            console.log('🔄 重启应用...');
            this.currentPhase = 0;
            this.phaseResults = [];
            return await this.start();
        }
    }

    // 暴露到OTA命名空间
    window.OTA.ApplicationBootstrap = ApplicationBootstrap;

    console.log('✅ 应用启动协调器已加载');

})();
