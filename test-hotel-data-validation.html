<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>酒店数据验证测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #4CAF50;
            padding-bottom: 10px;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .section h3 {
            margin-top: 0;
            color: #2196F3;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-item {
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
            text-align: center;
        }
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        pre {
            background-color: #f4f4f4;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏨 酒店数据验证测试</h1>
        <p>此测试验证 hotels_by_region.js 文件是否正确加载，以及智能地址翻译是否正常工作。</p>
        
        <div class="section">
            <h3>🔧 测试控制</h3>
            <button onclick="runAllTests()">运行所有测试</button>
            <button onclick="testGlobalData()">测试全局数据</button>
            <button onclick="testAddressTranslation()">测试地址翻译</button>
            <button onclick="clearResults()">清除结果</button>
        </div>

        <div class="stats" id="statsContainer">
            <div class="stat-item">
                <div class="stat-value" id="hotelCount">-</div>
                <div class="stat-label">酒店总数</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="mappingCount">-</div>
                <div class="stat-label">映射总数</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="loadTime">-</div>
                <div class="stat-label">加载时间(ms)</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="testStatus">-</div>
                <div class="stat-label">测试状态</div>
            </div>
        </div>

        <div class="section">
            <h3>📊 测试结果</h3>
            <div id="testResults"></div>
        </div>

        <div class="section">
            <h3>🔍 详细信息</h3>
            <div id="detailResults"></div>
        </div>
    </div>

    <!-- 加载脚本 -->
    <script src="js/core/script-manifest.js"></script>
    <script src="js/loader/script-loader.js"></script>

    <script>
        // 测试状态
        let testState = {
            globalDataLoaded: false,
            addressProcessorReady: false,
            startTime: null
        };

        // 输出结果函数
        function addResult(message, type = 'info', target = 'testResults') {
            const container = document.getElementById(target);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            container.appendChild(div);
            container.scrollTop = container.scrollHeight;
        }

        function updateStats(hotelCount, mappingCount, loadTime, status) {
            document.getElementById('hotelCount').textContent = hotelCount || '-';
            document.getElementById('mappingCount').textContent = mappingCount || '-';
            document.getElementById('loadTime').textContent = loadTime || '-';
            document.getElementById('testStatus').textContent = status || '-';
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
            document.getElementById('detailResults').innerHTML = '';
            updateStats('-', '-', '-', '-');
        }

        // 测试全局数据加载
        function testGlobalData() {
            addResult('🔍 开始测试全局酒店数据...', 'info');
            
            try {
                // 检查 window.OTA.hotelData
                if (window.OTA && window.OTA.hotelData) {
                    const hotelData = window.OTA.hotelData;
                    if (Array.isArray(hotelData)) {
                        addResult(`✅ 全局酒店数据已加载，共 ${hotelData.length} 条记录`, 'success');
                        
                        // 检查数据格式
                        let validCount = 0;
                        let sampleHotels = [];
                        
                        for (let i = 0; i < Math.min(hotelData.length, 10); i++) {
                            const hotel = hotelData[i];
                            if (hotel.chinese_name && hotel.english_name) {
                                validCount++;
                                if (sampleHotels.length < 5) {
                                    sampleHotels.push(`${hotel.chinese_name} -> ${hotel.english_name}`);
                                }
                            }
                        }
                        
                        addResult(`✅ 检查前10条记录，${validCount} 条格式正确`, 'success');
                        
                        // 特别检查时尚菲斯酒店
                        const faceStyleHotels = hotelData.filter(hotel => 
                            hotel.chinese_name && hotel.chinese_name.includes('时尚菲斯')
                        );
                        
                        if (faceStyleHotels.length > 0) {
                            addResult(`✅ 找到时尚菲斯相关酒店 ${faceStyleHotels.length} 个`, 'success');
                            faceStyleHotels.forEach(hotel => {
                                addResult(`  - ${hotel.chinese_name} -> ${hotel.english_name}`, 'info');
                            });
                        } else {
                            addResult('⚠️ 未找到时尚菲斯酒店记录', 'warning');
                        }
                        
                        // 显示样本数据
                        addResult(`📝 样本数据:\n${sampleHotels.join('\n')}`, 'info', 'detailResults');
                        
                        updateStats(hotelData.length, validCount, '-', '数据已加载');
                        testState.globalDataLoaded = true;
                        
                    } else {
                        addResult('❌ window.OTA.hotelData 不是数组格式', 'error');
                    }
                } else {
                    addResult('❌ window.OTA.hotelData 未定义', 'error');
                    addResult('💡 请确保 hotels_by_region.js 已正确加载并且包含 JavaScript 包装代码', 'warning');
                }
                
                // 检查脚本加载状态
                const scripts = document.querySelectorAll('script[src*="hotels_by_region"]');
                if (scripts.length > 0) {
                    addResult(`✅ 找到 hotels_by_region.js 脚本标签 ${scripts.length} 个`, 'success');
                } else {
                    addResult('⚠️ 未找到 hotels_by_region.js 脚本标签', 'warning');
                }
                
            } catch (error) {
                addResult(`❌ 测试全局数据时出错: ${error.message}`, 'error');
            }
        }

        // 测试地址翻译
        async function testAddressTranslation() {
            addResult('🤖 开始测试地址翻译功能...', 'info');
            
            try {
                // 等待地址处理器就绪
                if (!window.OTA || !window.OTA.simpleAddressProcessor) {
                    addResult('⏳ 等待地址处理器加载...', 'info');
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
                
                if (window.OTA && window.OTA.simpleAddressProcessor) {
                    const processor = window.OTA.simpleAddressProcessor;
                    addResult('✅ 地址处理器已就绪', 'success');
                    
                    // 获取处理器统计信息
                    const stats = processor.getStats();
                    addResult(`📊 处理器统计: 高频映射 ${stats.highFrequencyMappings}, 完整映射 ${stats.fullTranslationMappings}`, 'info');
                    
                    updateStats('-', stats.fullTranslationMappings, '-', '处理器就绪');
                    
                    // 测试关键酒店翻译
                    const testAddresses = [
                        '时尚菲斯酒店',
                        '菲斯时尚酒店', 
                        '菲斯酒店',
                        '双子塔',
                        'KLCC',
                        '吉隆坡国际机场'
                    ];
                    
                    addResult('🧪 开始测试关键地址翻译...', 'info');
                    
                    for (const address of testAddresses) {
                        try {
                            const startTime = Date.now();
                            const result = await processor.processAddress(address);
                            const endTime = Date.now();
                            
                            if (result.success) {
                                addResult(`✅ "${address}" -> "${result.processedAddress}" (${result.source}, ${endTime - startTime}ms)`, 'success');
                            } else {
                                addResult(`❌ 翻译失败: "${address}" - ${result.error}`, 'error');
                            }
                        } catch (error) {
                            addResult(`❌ 翻译出错: "${address}" - ${error.message}`, 'error');
                        }
                    }
                    
                    testState.addressProcessorReady = true;
                    
                } else {
                    addResult('❌ 地址处理器未加载', 'error');
                }
                
            } catch (error) {
                addResult(`❌ 测试地址翻译时出错: ${error.message}`, 'error');
            }
        }

        // 运行所有测试
        async function runAllTests() {
            clearResults();
            testState.startTime = Date.now();
            updateStats('-', '-', '-', '测试中...');
            
            addResult('🚀 开始完整测试流程...', 'info');
            
            // 等待脚本加载完成
            addResult('⏳ 等待脚本加载完成...', 'info');
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // 测试1: 全局数据
            testGlobalData();
            
            // 等待一段时间再测试地址翻译
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 测试2: 地址翻译
            await testAddressTranslation();
            
            // 总结
            const totalTime = Date.now() - testState.startTime;
            updateStats('-', '-', totalTime, '测试完成');
            
            if (testState.globalDataLoaded && testState.addressProcessorReady) {
                addResult(`🎉 所有测试完成！总耗时: ${totalTime}ms`, 'success');
            } else {
                addResult(`⚠️ 部分测试失败，请检查配置`, 'warning');
            }
        }

        // 页面加载完成后自动运行测试
        window.addEventListener('load', () => {
            addResult('📄 页面加载完成，准备测试...', 'info');
            setTimeout(runAllTests, 1000);
        });
    </script>
</body>
</html>