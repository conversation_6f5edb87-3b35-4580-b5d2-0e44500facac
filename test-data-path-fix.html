<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>数据路径修复测试</title>
    <style>
        body { font-family: monospace; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .log { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 4px; overflow-x: auto; }
        .error { background: #f8d7da; color: #721c24; }
        .success { background: #d4edda; color: #155724; }
        .warn { background: #fff3cd; color: #856404; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { white-space: pre-wrap; word-break: break-word; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 数据路径修复测试</h1>
        <p>测试修复后的地址翻译数据路径</p>
        
        <button onclick="testDataPathFix()">测试数据路径修复</button>
        <button onclick="clearLogs()">清空日志</button>
        
        <div id="logs"></div>
    </div>

    <!-- 核心系统脚本 -->
    <script src="js/core/logger.js"></script>
    <script src="js/core/dependency-container.js"></script>
    <script src="js/flow/gemini-caller.js"></script>
    <script src="js/flow/simple-address-processor.js"></script>
    <script src="js/data/essential-hotel-data.js"></script>
    <script src="js/flow/knowledge-base.js"></script>

    <script>
        let logs = [];

        // 捕获所有日志
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        function captureLog(level, ...args) {
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : arg
            ).join(' ');
            
            logs.push({
                level,
                message,
                timestamp: new Date().toLocaleTimeString()
            });
            
            updateLogs();
            
            // 调用原始方法
            const original = level === 'error' ? originalError : 
                           level === 'warn' ? originalWarn : originalLog;
            original.apply(console, args);
        }

        console.log = (...args) => captureLog('info', ...args);
        console.error = (...args) => captureLog('error', ...args);
        console.warn = (...args) => captureLog('warn', ...args);

        function updateLogs() {
            const container = document.getElementById('logs');
            container.innerHTML = logs.map(log => {
                const className = log.level === 'error' ? 'log error' :
                                log.level === 'warn' ? 'log warn' :
                                log.message.includes('✅') ? 'log success' : 'log';
                
                return `<div class="${className}">
                    [${log.timestamp}] <pre>${log.message}</pre>
                </div>`;
            }).join('');
            
            container.scrollTop = container.scrollHeight;
        }

        function clearLogs() {
            logs = [];
            updateLogs();
        }

        async function testDataPathFix() {
            console.log('🚀 开始测试数据路径修复...');
            
            try {
                // 确保系统初始化
                if (!window.OTA || !window.OTA.addressPipelineCoordinator) {
                    if (window.AddressPipelineCoordinator) {
                        window.OTA = window.OTA || {};
                        window.OTA.addressPipelineCoordinator = new window.AddressPipelineCoordinator();
                        console.log('✅ 创建AddressPipelineCoordinator实例');
                    } else {
                        throw new Error('AddressPipelineCoordinator未找到');
                    }
                }

                const testAddress = 'ROYCE悦马都高级公寓近双子塔KLCC无边泳池DORMEO DESTINATIONS';
                console.log(`📍 测试地址: ${testAddress}`);

                // 处理地址
                const result = await window.OTA.addressPipelineCoordinator.processAddress(testAddress);
                
                console.log('📊 处理结果:', JSON.stringify(result, null, 2));
                
                if (result.success) {
                    if (result.processedAddress !== result.originalAddress) {
                        console.log(`✅ 地址成功标准化: ${result.originalAddress} → ${result.processedAddress}`);
                    } else {
                        console.log(`⚠️ 地址未发生变化: ${result.processedAddress}`);
                    }
                } else {
                    console.log('❌ 地址处理失败');
                }
                
            } catch (error) {
                console.error('❌ 测试失败:', error.message);
                console.error('错误详情:', error);
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                console.log('🚀 数据路径修复测试页面已加载');
            }, 1000);
        });
    </script>
</body>
</html>