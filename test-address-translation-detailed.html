<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>详细地址翻译测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f7fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e1e8ed;
            border-radius: 8px;
            background-color: #fafbfc;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: background 0.3s;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .results {
            margin-top: 20px;
            padding: 15px;
            background: white;
            border-radius: 6px;
            border-left: 4px solid #28a745;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            overflow-x: auto;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            border-left-color: #dc3545;
            color: #721c24;
            background-color: #f8d7da;
        }
        .info {
            border-left-color: #17a2b8;
            color: #0c5460;
            background-color: #d1ecf1;
        }
        .address-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .form-group input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .address-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .address-box {
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #ddd;
        }
        .original { background-color: #fff3cd; }
        .processed { background-color: #d4edda; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 详细地址翻译测试</h1>
            <p>测试中文地址到英文的完整翻译流程</p>
        </div>

        <div class="test-section">
            <h3>📋 手动地址翻译测试</h3>
            <div class="address-form">
                <div class="form-group">
                    <label>测试地址（中文）:</label>
                    <input type="text" id="testAddress" 
                           value="ROYCE悦马都高级公寓近双子塔KLCC无边泳池DORMEO DESTINATIONS" 
                           placeholder="输入需要翻译的中文地址">
                </div>
                <button class="test-button" onclick="testSingleAddressTranslation()">开始翻译测试</button>
                <button class="test-button" onclick="testPipelineFlow()">测试完整流水线</button>
                <button class="test-button" onclick="clearResults()">清空结果</button>
            </div>
            
            <div class="address-comparison" id="addressComparison" style="display: none;">
                <div class="address-box original">
                    <h4>原始地址</h4>
                    <p id="originalAddress"></p>
                </div>
                <div class="address-box processed">
                    <h4>处理后地址</h4>
                    <p id="processedAddress"></p>
                </div>
            </div>
            
            <div class="results" id="testResults"></div>
        </div>

        <div class="test-section">
            <h3>📊 实时处理日志</h3>
            <div class="results" id="realTimeLog"></div>
        </div>
    </div>

    <!-- 核心系统脚本 -->
    <script src="js/core/logger.js"></script>
    <script src="js/core/dependency-container.js"></script>
    <script src="js/flow/gemini-caller.js"></script>
    <script src="js/flow/simple-address-processor.js"></script>
    <script src="js/data/essential-hotel-data.js"></script>
    <script src="js/flow/knowledge-base.js"></script>

    <script>
        let testResults = [];
        let logEntries = [];

        // 捕获所有日志
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;

        function captureLog(level, ...args) {
            const entry = {
                level,
                message: args.map(arg => typeof arg === 'object' ? JSON.stringify(arg, null, 2) : arg).join(' '),
                timestamp: new Date().toLocaleTimeString()
            };
            
            logEntries.push(entry);
            updateRealTimeLog();
            
            // 调用原始console方法
            const originalMethod = level === 'error' ? originalConsoleError : 
                                 level === 'warn' ? originalConsoleWarn : originalConsoleLog;
            originalMethod.apply(console, args);
        }

        console.log = (...args) => captureLog('info', ...args);
        console.error = (...args) => captureLog('error', ...args);
        console.warn = (...args) => captureLog('warn', ...args);

        function updateRealTimeLog() {
            const logContainer = document.getElementById('realTimeLog');
            const recentLogs = logEntries.slice(-30); // 显示最近30条日志
            
            logContainer.innerHTML = recentLogs.map(entry => {
                const className = entry.level === 'error' ? 'error' : 
                                entry.level === 'warn' ? 'info' : '';
                return `<div style="margin: 2px 0; padding: 2px;">[${entry.timestamp}] ${entry.message}</div>`;
            }).join('');
            
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function addResult(message, type = 'info') {
            testResults.push({
                message,
                type,
                timestamp: new Date().toLocaleTimeString()
            });
            updateResults();
        }

        function updateResults() {
            const resultsContainer = document.getElementById('testResults');
            resultsContainer.innerHTML = testResults.map(result => {
                const className = result.type === 'error' ? 'error' : 
                                result.type === 'success' ? '' : 'info';
                return `[${result.timestamp}] ${result.message}`;
            }).join('\n');
            
            resultsContainer.scrollTop = resultsContainer.scrollHeight;
        }

        function clearResults() {
            testResults = [];
            logEntries = [];
            updateResults();
            updateRealTimeLog();
            document.getElementById('addressComparison').style.display = 'none';
        }

        function showAddressComparison(original, processed) {
            document.getElementById('originalAddress').textContent = original;
            document.getElementById('processedAddress').textContent = processed || '未处理';
            document.getElementById('addressComparison').style.display = 'grid';
        }

        // 测试单个地址翻译
        async function testSingleAddressTranslation() {
            const address = document.getElementById('testAddress').value.trim();
            if (!address) {
                addResult('❌ 请输入测试地址', 'error');
                return;
            }

            addResult(`🚀 开始测试地址翻译: ${address}`, 'info');
            
            try {
                if (!window.OTA || !window.OTA.addressTranslator) {
                    throw new Error('AddressTranslator未找到');
                }

                const translator = window.OTA.addressTranslator;
                
                // 1. 直接翻译测试
                addResult('📝 步骤1: 直接地址翻译', 'info');
                const directResult = await translator.translateAddress(address, ['en', 'ms']);
                addResult(`直接翻译结果: ${JSON.stringify(directResult, null, 2)}`, 'info');
                
                // 2. Gemini处理测试
                addResult('🤖 步骤2: Gemini处理', 'info');
                if (translator.processWithGemini) {
                    const geminiResult = await translator.processWithGemini(address);
                    addResult(`Gemini处理结果: ${JSON.stringify(geminiResult, null, 2)}`, geminiResult.success ? 'success' : 'error');
                    
                    if (geminiResult.success && geminiResult.data?.standardizedAddress) {
                        showAddressComparison(address, geminiResult.data.standardizedAddress);
                        addResult(`✅ Gemini标准化地址: ${geminiResult.data.standardizedAddress}`, 'success');
                    }
                } else {
                    addResult('❌ processWithGemini方法不可用', 'error');
                }

                addResult('✅ 单个地址翻译测试完成', 'success');
                
            } catch (error) {
                addResult(`❌ 地址翻译测试失败: ${error.message}`, 'error');
                console.error('地址翻译测试错误:', error);
            }
        }

        // 测试完整流水线
        async function testPipelineFlow() {
            const address = document.getElementById('testAddress').value.trim();
            if (!address) {
                addResult('❌ 请输入测试地址', 'error');
                return;
            }

            addResult(`🔄 开始测试完整地址处理流水线: ${address}`, 'info');
            
            try {
                // 确保流水线协调器存在
                if (!window.OTA || !window.OTA.addressPipelineCoordinator) {
                    if (window.AddressPipelineCoordinator) {
                        window.OTA = window.OTA || {};
                        window.OTA.addressPipelineCoordinator = new window.AddressPipelineCoordinator();
                        addResult('✅ 创建了AddressPipelineCoordinator实例', 'success');
                    } else {
                        throw new Error('AddressPipelineCoordinator类未找到');
                    }
                }

                const coordinator = window.OTA.addressPipelineCoordinator;
                
                addResult('🚀 调用流水线处理...', 'info');
                const result = await coordinator.processAddress(address);
                
                addResult(`流水线处理结果: ${JSON.stringify(result, null, 2)}`, result.success ? 'success' : 'error');
                
                if (result.success) {
                    showAddressComparison(result.originalAddress, result.processedAddress);
                    
                    if (result.processedAddress !== result.originalAddress) {
                        addResult(`✅ 地址已标准化: ${result.originalAddress} → ${result.processedAddress}`, 'success');
                    } else {
                        addResult(`⚠️ 地址未发生变化: ${result.processedAddress}`, 'info');
                    }
                    
                    // 分析结果详情
                    if (result.results?.addressStandardization) {
                        addResult(`📊 标准化信息: 来源=${result.results.addressStandardization.source}, 置信度=${result.results.addressStandardization.confidence}`, 'info');
                    }
                }
                
                addResult('✅ 完整流水线测试完成', 'success');
                
            } catch (error) {
                addResult(`❌ 流水线测试失败: ${error.message}`, 'error');
                console.error('流水线测试错误:', error);
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                addResult('🚀 地址翻译详细测试页面已加载', 'info');
                
                // 检查系统状态
                const status = [];
                status.push(`AddressTranslator: ${!!(window.OTA && window.OTA.addressTranslator) ? '✅' : '❌'}`);
                status.push(`GeminiCaller: ${!!(window.OTA && window.OTA.geminiCaller) ? '✅' : '❌'}`);
                status.push(`AddressPipelineCoordinator: ${!!(window.OTA && window.OTA.addressPipelineCoordinator) ? '✅' : '❌'}`);
                
                addResult(`系统组件状态: ${status.join(' | ')}`, 'info');
            }, 1000);
        });
    </script>
</body>
</html>